---
description: 
globs: 
alwaysApply: true
---
# FastAPI Project Rules for Request/Response Format

## ✅ REQUEST MODEL RULES
- Every API endpoint must use a `pydantic.BaseModel` class for the request body.
- Request model class names must end with `Request` (e.g., `LoginRequest`, `UserCreateRequest`).
- Fields must use type hints and include examples where helpful.
- Prefer primitive types: `str`, `int`, `float`, `bool`, `List`, `Dict`.

## ✅ RESPONSE MODEL RULES
- Every endpoint must declare a `response_model` using a `pydantic.BaseModel` class.
- Response model class names must end with `Response` (e.g., `LoginResponse`, `UserInfoResponse`).
- All responses should be structured and never return raw dicts, unless explicitly specified.
- Response fields must use clear types and descriptions when possible.

## ✅ ENUM USAGE
- Use `Enum` (subclassed from `str`) for fields with limited fixed values.
- Enum values must be capitalized (e.g., `ADMIN`, `USER`).
- Example:
  ```python
  class UserRole(str, Enum):
      ADMIN = "ADMIN"
      USER = "USER"