# GrowthHive FastAPI Backend Dependencies
# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database & ORM
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0
greenlet>=3.0.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
bcrypt>=4.0.1
python-multipart>=0.0.6
PyJWT>=2.8.0

# Data Validation & Settings
pydantic>=2.5.0
pydantic-settings>=2.1.0
email-validator>=2.0.0

# HTTP Client & Testing
httpx>=0.25.0
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# File Handling & Storage
python-multipart>=0.0.6
boto3>=1.34.0
botocore>=1.34.0

# AI & Machine Learning
openai>=1.3.0
numpy>=1.24.0

# Utilities
python-dotenv>=1.0.0
python-dateutil>=2.8.2
pytz>=2023.3

# Development & Code Quality
ruff>=0.1.0
mypy>=1.7.0
black>=23.0.0

# Logging & Monitoring
structlog>=23.2.0

# Optional: Additional dependencies for enhanced functionality
# redis>=5.0.0  # For caching (uncomment if using Redis)
# celery>=5.3.0  # For background tasks (uncomment if using Celery)
# prometheus-client>=0.19.0  # For metrics (uncomment if using Prometheus) 