from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Optional
from uuid import UUID
from app.schemas.messaging_rule import (
    MessagingRuleCreateRequest,
    MessagingRuleUpdateRequest,
    MessagingRuleResponse,
    MessagingRuleListResponse
)
from app.core.factory import get_messaging_rule_service
from app.services.messaging_rule_service import MessagingRuleService
from app.core.security.enhanced_auth_middleware import get_current_active_user

router = APIRouter(prefix="/settings/messaging-rules", tags=["Messaging Rule Config"])

@router.get("/", response_model=MessagingRuleListResponse)
async def list_messaging_rules(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    user=Depends(get_current_active_user)
):
    """List messaging rules with pagination."""
    return await service.list(skip=skip, limit=limit)

@router.get("/{rule_id}", response_model=MessagingRuleResponse)
async def get_messaging_rule(
    rule_id: UUID,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    user=Depends(get_current_active_user)
):
    """Retrieve a messaging rule by ID."""
    rule = await service.get(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="Messaging rule not found")
    return rule

@router.post("/", response_model=MessagingRuleResponse, status_code=status.HTTP_201_CREATED)
async def create_messaging_rule(
    data: MessagingRuleCreateRequest,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    user=Depends(get_current_active_user)
):
    """Create a new messaging rule. Only one active rule is allowed."""
    return await service.create(data)

@router.put("/{rule_id}", response_model=MessagingRuleResponse)
async def update_messaging_rule(
    rule_id: UUID,
    data: MessagingRuleUpdateRequest,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    user=Depends(get_current_active_user)
):
    """Update an existing messaging rule. Only one active rule is allowed."""
    rule = await service.update(rule_id, data)
    if not rule:
        raise HTTPException(status_code=404, detail="Messaging rule not found")
    return rule

@router.delete("/{rule_id}", response_model=MessagingRuleResponse)
async def delete_messaging_rule(
    rule_id: UUID,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    user=Depends(get_current_active_user)
):
    """Soft-delete a messaging rule."""
    rule = await service.soft_delete(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="Messaging rule not found")
    return rule 