from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Optional
from uuid import UUID
from app.schemas.holiday import (
    HolidayCreateRequest,
    HolidayUpdateRequest,
    HolidayResponse,
    HolidayListResponse
)
from app.core.factory import get_holiday_service
from app.services.holiday_service import HolidayService
from app.core.security.enhanced_auth_middleware import get_current_active_user

router = APIRouter(prefix="/settings/holidays", tags=["Holiday Management"])

@router.get("/", response_model=HolidayListResponse)
async def list_holidays(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    service: HolidayService = Depends(get_holiday_service),
    user=Depends(get_current_active_user)
):
    """List holidays with optional search and pagination."""
    return await service.list(skip=skip, limit=limit, search=search)

@router.get("/{holiday_id}", response_model=HolidayResponse)
async def get_holiday(
    holiday_id: UUID,
    service: HolidayService = Depends(get_holiday_service),
    user=Depends(get_current_active_user)
):
    """Retrieve a holiday by ID."""
    holiday = await service.get(holiday_id)
    if not holiday:
        raise HTTPException(status_code=404, detail="Holiday not found")
    return holiday

@router.post("/", response_model=HolidayResponse, status_code=status.HTTP_201_CREATED)
async def create_holiday(
    data: HolidayCreateRequest,
    service: HolidayService = Depends(get_holiday_service),
    user=Depends(get_current_active_user)
):
    """Create a new holiday."""
    return await service.create(data)

@router.put("/{holiday_id}", response_model=HolidayResponse)
async def update_holiday(
    holiday_id: UUID,
    data: HolidayUpdateRequest,
    service: HolidayService = Depends(get_holiday_service),
    user=Depends(get_current_active_user)
):
    """Update an existing holiday."""
    holiday = await service.update(holiday_id, data)
    if not holiday:
        raise HTTPException(status_code=404, detail="Holiday not found")
    return holiday

@router.delete("/{holiday_id}", response_model=HolidayResponse)
async def delete_holiday(
    holiday_id: UUID,
    service: HolidayService = Depends(get_holiday_service),
    user=Depends(get_current_active_user)
):
    """Soft-delete a holiday."""
    holiday = await service.soft_delete(holiday_id)
    if not holiday:
        raise HTTPException(status_code=404, detail="Holiday not found")
    return holiday 