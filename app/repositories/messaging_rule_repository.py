"""
Messaging Rule Repository
Data access layer for messaging rule configuration
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy import select, and_, func, desc, update
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.messaging_rule import MessagingRule
from app.schemas.messaging_rule import MessagingRuleCreateRequest, MessagingRuleUpdateRequest
from app.repositories.base import BaseRepository
from app.core.logging import logger


class MessagingRuleRepository(BaseRepository[MessagingRule, MessagingRuleCreateRequest, MessagingRuleUpdateRequest]):
    """Repository for messaging rule configuration operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(MessagingRule, db)
    
    async def get_active_rules(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[MessagingRule]:
        """
        Get active messaging rules (not deleted)
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of active messaging rules
        """
        query = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_current_active_rule(self) -> Optional[MessagingRule]:
        """
        Get the currently active messaging rule
        Business rule: Only one rule should be active at a time
        
        Returns:
            The currently active messaging rule or None
        """
        query = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).limit(1)
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def deactivate_all_rules(self) -> int:
        """
        Deactivate all currently active messaging rules
        Used to enforce single active rule business logic
        
        Returns:
            Number of rules deactivated
        """
        query = update(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        ).values(
            is_active=False,
            updated_at=datetime.now(timezone.utc)
        )
        
        result = await self.db.execute(query)
        await self.db.commit()
        
        deactivated_count = result.rowcount
        if deactivated_count > 0:
            logger.info(f"Deactivated {deactivated_count} messaging rules to enforce single active rule")
        
        return deactivated_count
    
    async def create_with_single_active_enforcement(
        self, 
        obj_in: MessagingRuleCreateRequest
    ) -> MessagingRule:
        """
        Create a new messaging rule and ensure only one is active
        
        Args:
            obj_in: Messaging rule creation data
            
        Returns:
            Created messaging rule instance
        """
        # First deactivate all existing active rules
        await self.deactivate_all_rules()
        
        # Create the new rule (it will be active by default)
        obj_data = obj_in.model_dump()
        obj_data['is_active'] = True  # Ensure new rule is active
        
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        
        logger.info(f"Created new active messaging rule: {db_obj.id}")
        return db_obj
    
    async def update_with_single_active_enforcement(
        self, 
        rule_id: UUID, 
        obj_in: MessagingRuleUpdateRequest
    ) -> Optional[MessagingRule]:
        """
        Update a messaging rule and handle single active rule enforcement
        
        Args:
            rule_id: ID of the rule to update
            obj_in: Update data
            
        Returns:
            Updated messaging rule instance or None if not found
        """
        # Get the existing rule
        existing_rule = await self.get(rule_id)
        if not existing_rule or existing_rule.is_deleted:
            return None
        
        # If setting this rule to active, deactivate all others first
        if obj_in.is_active is True:
            await self.deactivate_all_rules()
        
        # Update the rule
        update_data = obj_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(existing_rule, field, value)
        
        existing_rule.updated_at = datetime.now(timezone.utc)
        
        await self.db.commit()
        await self.db.refresh(existing_rule)
        
        logger.info(f"Updated messaging rule: {rule_id}")
        return existing_rule
    
    async def soft_delete(self, rule_id: UUID) -> Optional[MessagingRule]:
        """
        Soft delete a messaging rule by setting is_deleted=True and deleted_at=now()
        
        Args:
            rule_id: ID of the rule to delete
            
        Returns:
            Updated messaging rule instance or None if not found
        """
        rule = await self.get(rule_id)
        if not rule:
            return None
        
        rule.is_deleted = True
        rule.is_active = False  # Also deactivate when deleting
        rule.deleted_at = datetime.now(timezone.utc)
        rule.updated_at = datetime.now(timezone.utc)
        
        await self.db.commit()
        await self.db.refresh(rule)
        
        logger.info(f"Messaging rule soft deleted: {rule_id}")
        return rule
    
    async def count_active_rules(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count active messaging rules with optional filtering
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            Number of active messaging rules
        """
        query = select(func.count(self.model.id)).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        )
        
        # Apply additional filters if provided
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.where(getattr(self.model, field) == value)
        
        result = await self.db.execute(query)
        return result.scalar() or 0
    
    async def get_all_rules_including_inactive(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[MessagingRule]:
        """
        Get all messaging rules including inactive ones (but not deleted)
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of all non-deleted messaging rules
        """
        query = select(self.model).where(
            self.model.is_deleted == False
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
