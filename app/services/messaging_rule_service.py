"""
Messaging Rule Service
Business logic layer for messaging rule configuration
"""

from typing import List, Optional, Union, Dict, Any
from uuid import UUID
from fastapi import status
from app.models.messaging_rule import MessagingRule
from app.schemas.messaging_rule import MessagingRuleCreateRequest, MessagingRuleUpdateRequest
from app.repositories.messaging_rule_repository import MessagingRuleRepository
from app.core.api_standards import APIStandards
from app.core.logging import logger


class MessagingRuleService:
    """Service for messaging rule configuration operations"""
    
    def __init__(self, repository: MessagingRuleRepository):
        self.repository = repository
    
    async def create_messaging_rule(self, obj_in: MessagingRuleCreateRequest) -> Union[MessagingRule, dict]:
        """
        Create a new messaging rule with single active rule enforcement
        
        Args:
            obj_in: Messaging rule creation data
            
        Returns:
            Created messaging rule instance or error response
        """
        try:
            # Create the rule with single active enforcement
            result = await self.repository.create_with_single_active_enforcement(obj_in)
            
            if not isinstance(result, MessagingRule):
                return APIStandards.create_error_response(
                    error_message="Failed to create messaging rule",
                    error_title="Creation Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=5001
                )
            
            logger.info(f"Messaging rule created successfully: {result.id}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating messaging rule: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while creating the messaging rule",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5001
            )
    
    async def get_messaging_rule(self, rule_id: str) -> Union[MessagingRule, dict]:
        """
        Get a messaging rule by ID
        
        Args:
            rule_id: Messaging rule UUID
            
        Returns:
            Messaging rule instance or error response
        """
        try:
            rule_uuid = UUID(rule_id)
            rule = await self.repository.get(rule_uuid)
            
            if not rule or rule.is_deleted:
                return APIStandards.create_error_response(
                    error_message="Messaging rule not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004
                )
            
            return rule
            
        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid messaging rule ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002
            )
        except Exception as e:
            logger.error(f"Error retrieving messaging rule {rule_id}: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while retrieving the messaging rule",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5002
            )
    
    async def update_messaging_rule(self, rule_id: str, obj_in: MessagingRuleUpdateRequest) -> Union[MessagingRule, dict]:
        """
        Update a messaging rule with single active rule enforcement
        
        Args:
            rule_id: Messaging rule UUID
            obj_in: Messaging rule update data
            
        Returns:
            Updated messaging rule instance or error response
        """
        try:
            rule_uuid = UUID(rule_id)
            
            # Update the rule with single active enforcement
            result = await self.repository.update_with_single_active_enforcement(rule_uuid, obj_in)
            
            if not result:
                return APIStandards.create_error_response(
                    error_message="Messaging rule not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004
                )
            
            logger.info(f"Messaging rule updated successfully: {rule_id}")
            return result
            
        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid messaging rule ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002
            )
        except Exception as e:
            logger.error(f"Error updating messaging rule {rule_id}: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while updating the messaging rule",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5003
            )
    
    async def delete_messaging_rule(self, rule_id: str) -> Union[bool, dict]:
        """
        Soft delete a messaging rule
        
        Args:
            rule_id: Messaging rule UUID
            
        Returns:
            True if successful or error response
        """
        try:
            rule_uuid = UUID(rule_id)
            result = await self.repository.soft_delete(rule_uuid)
            
            if not result:
                return APIStandards.create_error_response(
                    error_message="Messaging rule not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004
                )
            
            logger.info(f"Messaging rule deleted successfully: {rule_id}")
            return True
            
        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid messaging rule ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002
            )
        except Exception as e:
            logger.error(f"Error deleting messaging rule {rule_id}: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while deleting the messaging rule",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5004
            )
    
    async def list_messaging_rules(
        self,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = False
    ) -> Union[List[MessagingRule], dict]:
        """
        List messaging rules with optional filtering
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_inactive: Whether to include inactive rules
            
        Returns:
            List of messaging rules or error response
        """
        try:
            if include_inactive:
                return await self.repository.get_all_rules_including_inactive(skip, limit)
            else:
                return await self.repository.get_active_rules(skip, limit)
                
        except Exception as e:
            logger.error(f"Error listing messaging rules: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while retrieving messaging rules",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5005
            )
    
    async def get_current_active_rule(self) -> Union[MessagingRule, dict, None]:
        """
        Get the currently active messaging rule
        
        Returns:
            Currently active messaging rule, None if no active rule, or error response
        """
        try:
            return await self.repository.get_current_active_rule()
        except Exception as e:
            logger.error(f"Error getting current active messaging rule: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while retrieving the active messaging rule",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5006
            )
    
    async def count_messaging_rules(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count messaging rules with optional filtering
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            Number of messaging rules
        """
        try:
            return await self.repository.count_active_rules(filters)
        except Exception as e:
            logger.error(f"Error counting messaging rules: {str(e)}", exc_info=True)
            return 0
