"""Lead service for business logic"""
import uuid
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy import select, update, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

from app.models.lead import Lead
from app.schemas.lead import (
    LeadCreateRequest,
    LeadUpdateRequest,
    LeadResponse,
    LeadStatus
)
from app.core.logging import logger


class LeadService:
    """Service for lead-related business logic"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    def _generate_lead_id(self) -> str:
        """Generate a unique lead ID"""
        return str(uuid.uuid4())
    
    async def create_lead(self, lead_data: LeadCreateRequest, created_by: str = None) -> Lead:
        """Create a new lead"""
        try:
            # Prepare lead data, explicitly excluding created_at and updated_at
            lead_kwargs = dict(
                id=uuid.uuid4(),
                zoho_lead_id=lead_data.zoho_lead_id,
                full_name=lead_data.full_name,
                contact_number=lead_data.contact_number,
                email=lead_data.email,
                location=lead_data.location,
                lead_source=lead_data.lead_source,
                franchise_preference=lead_data.franchise_preference,
                budget_preference=lead_data.budget_preference,
                qualification_status=lead_data.qualification_status.value if lead_data.qualification_status else LeadStatus.NEW.value
            )
            # If the Lead model supports created_by, set it
            if hasattr(Lead, "created_by") and created_by:
                lead_kwargs["created_by"] = created_by
            lead = Lead(**lead_kwargs)
            self.db.add(lead)
            await self.db.commit()
            await self.db.refresh(lead)
            logger.info(f"Created lead: {lead.id}")
            return lead
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating lead: {e}")
            raise
    
    async def get_lead_by_id(self, lead_id: str, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Get a lead by ID with optional permission checking"""
        try:
            query = select(Lead).where(Lead.id == uuid.UUID(lead_id))
            result = await self.db.execute(query)
            lead = result.scalar_one_or_none()
            
            # Admin can see all leads, regular users can only see their assigned leads
            if lead and user_role and user_role != "ADMIN":
                # For now, allow all users to see all leads
                # TODO: Implement proper lead assignment logic
                pass
                
            return lead
        except Exception as e:
            logger.error(f"Error getting lead: {e}")
            raise
    
    async def get_leads(
        self,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        search: Optional[str] = None,
        user_role: str = None,
        user_id: str = None
    ) -> Tuple[List[LeadResponse], int]:
        """Get all leads with pagination, filtering, and permission checking"""
        try:
            # Build query
            query = select(Lead)
            
            # Apply filters
            filters = []
            if status:
                filters.append(Lead.qualification_status == status)
            if search:
                # Case-insensitive search by name, email, or contact number
                search_filter = or_(
                    Lead.full_name.ilike(f"%{search}%"),
                    Lead.email.ilike(f"%{search}%"),
                    Lead.contact_number.ilike(f"%{search}%"),
                    Lead.location.ilike(f"%{search}%")
                )
                filters.append(search_filter)
            
            if filters:
                query = query.where(and_(*filters))
            
            # Get total count
            count_query = select(func.count(Lead.id))
            if filters:
                count_query = count_query.where(and_(*filters))
            
            total_count = await self.db.scalar(count_query)
            
            # Apply pagination and ordering
            query = query.order_by(desc(Lead.created_at)).offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            leads = result.scalars().all()
            
            # Convert to response models
            lead_responses = []
            for lead in leads:
                lead_response = LeadResponse(
                    id=str(lead.id),
                    zoho_lead_id=lead.zoho_lead_id,
                    full_name=lead.full_name,
                    contact_number=lead.contact_number,
                    email=lead.email,
                    location=lead.location,
                    lead_source=lead.lead_source,
                    franchise_preference=lead.franchise_preference,
                    budget_preference=lead.budget_preference,
                    qualification_status=LeadStatus(lead.qualification_status),
                    created_at=lead.created_at,
                    updated_at=lead.updated_at
                )
                lead_responses.append(lead_response)
            
            return lead_responses, total_count
            
        except Exception as e:
            logger.error(f"Error getting leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve leads"
            )
    
    async def update_lead(self, lead_id: str, lead_data: LeadUpdateRequest, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Update a lead with permission checking"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return None
            
            # Prepare update data, excluding None values
            update_data = {}
            if lead_data.full_name is not None:
                update_data["full_name"] = lead_data.full_name
            if lead_data.contact_number is not None:
                update_data["contact_number"] = lead_data.contact_number
            if lead_data.email is not None:
                update_data["email"] = lead_data.email
            if lead_data.location is not None:
                update_data["location"] = lead_data.location
            if lead_data.lead_source is not None:
                update_data["lead_source"] = lead_data.lead_source
            if lead_data.franchise_preference is not None:
                update_data["franchise_preference"] = lead_data.franchise_preference
            if lead_data.budget_preference is not None:
                update_data["budget_preference"] = lead_data.budget_preference
            if lead_data.qualification_status is not None:
                update_data["qualification_status"] = lead_data.qualification_status.value
            
            if update_data:
                await self.db.execute(
                    update(Lead)
                    .where(Lead.id == uuid.UUID(lead_id))
                    .values(**update_data)
                )
                await self.db.commit()
            
            return await self.get_lead_by_id(lead_id, user_role, user_id)
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating lead: {e}")
            raise
    
    async def delete_lead(self, lead_id: str, user_role: str = None, user_id: str = None) -> bool:
        """Delete a lead with permission checking"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return False
            
            await self.db.delete(lead)
            await self.db.commit()
            return True
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting lead: {e}")
            raise
    
    async def search_leads(
        self,
        query: str,
        skip: int = 0,
        limit: int = 20
    ) -> List[LeadResponse]:
        """Search leads by query"""
        try:
            search_query = select(Lead).where(
                or_(
                    Lead.full_name.ilike(f"%{query}%"),
                    Lead.email.ilike(f"%{query}%"),
                    Lead.contact_number.ilike(f"%{query}%"),
                    Lead.location.ilike(f"%{query}%")
                )
            ).offset(skip).limit(limit).order_by(desc(Lead.created_at))
            
            result = await self.db.execute(search_query)
            leads = result.scalars().all()
            
            # Convert to response models
            lead_responses = []
            for lead in leads:
                lead_response = LeadResponse(
                    id=str(lead.id),
                    zoho_lead_id=lead.zoho_lead_id,
                    full_name=lead.full_name,
                    contact_number=lead.contact_number,
                    email=lead.email,
                    location=lead.location,
                    lead_source=lead.lead_source,
                    franchise_preference=lead.franchise_preference,
                    budget_preference=lead.budget_preference,
                    qualification_status=LeadStatus(lead.qualification_status),
                    created_at=lead.created_at,
                    updated_at=lead.updated_at
                )
                lead_responses.append(lead_response)
            
            return lead_responses
        except Exception as e:
            logger.error(f"Error searching leads: {e}")
            raise
    
    async def assign_lead(self, lead_id: str, assigned_to: str) -> Optional[Lead]:
        """Assign a lead to a user"""
        try:
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                return None
            
            # TODO: Add assigned_to field to Lead model if needed
            # For now, just return the lead
            return lead
        except Exception as e:
            logger.error(f"Error assigning lead: {e}")
            raise
    
    async def update_lead_score(self, lead_id: str, score: int, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Update lead score"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return None
            
            # TODO: Add score field to Lead model if needed
            # For now, just return the lead
            return lead
        except Exception as e:
            logger.error(f"Error updating lead score: {e}")
            raise
