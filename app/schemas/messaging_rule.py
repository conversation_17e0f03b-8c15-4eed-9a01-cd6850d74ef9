"""
Messaging Rule Schemas
Pydantic models for messaging rule configuration API
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field, field_validator


class MessagingRuleBase(BaseModel):
    """Base messaging rule schema with common fields"""
    lead_init_delay_h: int = Field(
        ..., 
        ge=0,
        description="Initial delay in hours before first message to lead (must be >= 0)"
    )
    no_response_delay_h: int = Field(
        ..., 
        gt=0,
        description="Delay in hours between follow-up messages when no response (must be > 0)"
    )
    max_followups: int = Field(
        ..., 
        ge=0,
        description="Maximum number of follow-up messages allowed (must be >= 0)"
    )
    
    @field_validator('lead_init_delay_h')
    @classmethod
    def validate_lead_init_delay(cls, v):
        if v < 0:
            raise ValueError('lead_init_delay_h must be >= 0')
        return v
    
    @field_validator('no_response_delay_h')
    @classmethod
    def validate_no_response_delay(cls, v):
        if v <= 0:
            raise ValueError('no_response_delay_h must be > 0')
        return v
    
    @field_validator('max_followups')
    @classmethod
    def validate_max_followups(cls, v):
        if v < 0:
            raise ValueError('max_followups must be >= 0')
        return v


class MessagingRuleCreateRequest(MessagingRuleBase):
    """Schema for creating a new messaging rule"""
    
    class Config:
        json_schema_extra = {
            "example": {
                "lead_init_delay_h": 2,
                "no_response_delay_h": 24,
                "max_followups": 3
            }
        }


class MessagingRuleUpdateRequest(BaseModel):
    """Schema for updating an existing messaging rule"""
    lead_init_delay_h: Optional[int] = Field(
        None, 
        ge=0,
        description="Initial delay in hours before first message to lead (must be >= 0)"
    )
    no_response_delay_h: Optional[int] = Field(
        None, 
        gt=0,
        description="Delay in hours between follow-up messages when no response (must be > 0)"
    )
    max_followups: Optional[int] = Field(
        None, 
        ge=0,
        description="Maximum number of follow-up messages allowed (must be >= 0)"
    )
    is_active: Optional[bool] = Field(
        None, 
        description="Whether the messaging rule is active"
    )
    
    @field_validator('lead_init_delay_h')
    @classmethod
    def validate_lead_init_delay(cls, v):
        if v is not None and v < 0:
            raise ValueError('lead_init_delay_h must be >= 0')
        return v
    
    @field_validator('no_response_delay_h')
    @classmethod
    def validate_no_response_delay(cls, v):
        if v is not None and v <= 0:
            raise ValueError('no_response_delay_h must be > 0')
        return v
    
    @field_validator('max_followups')
    @classmethod
    def validate_max_followups(cls, v):
        if v is not None and v < 0:
            raise ValueError('max_followups must be >= 0')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "lead_init_delay_h": 4,
                "no_response_delay_h": 48,
                "max_followups": 5,
                "is_active": True
            }
        }


class MessagingRuleResponse(MessagingRuleBase):
    """Schema for messaging rule response"""
    id: UUID = Field(..., description="Messaging rule unique identifier")
    is_active: bool = Field(..., description="Whether the messaging rule is active")
    is_deleted: bool = Field(..., description="Whether the messaging rule is soft deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "lead_init_delay_h": 2,
                "no_response_delay_h": 24,
                "max_followups": 3,
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "deleted_at": None
            }
        }


class MessagingRuleListResponse(BaseModel):
    """Schema for messaging rule list response"""
    items: list[MessagingRuleResponse] = Field(..., description="List of messaging rules")
    total_count: int = Field(..., description="Total number of messaging rules")
    
    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "lead_init_delay_h": 2,
                        "no_response_delay_h": 24,
                        "max_followups": 3,
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z",
                        "deleted_at": None
                    }
                ],
                "total_count": 1
            }
        }
