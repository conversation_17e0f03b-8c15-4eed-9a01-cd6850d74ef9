"""
Holiday Schemas
Pydantic models for holiday management API
"""

from datetime import datetime, date, time
from typing import Optional, Literal
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic_core import ValidationError


class HolidayBase(BaseModel):
    """Base holiday schema with common fields"""

    holiday_type: Literal["PREDEFINED", "PERSONAL"] = Field(
        ...,
        description="Type of holiday: PREDEFINED for company-wide holidays, PERSONAL for individual holidays",
    )
    date: date = Field(..., description="Holiday date")
    all_day: bool = Field(True, description="Whether the holiday is all day")
    start_time: Optional[time] = Field(None, description="Start time if not all day")
    end_time: Optional[time] = Field(None, description="End time if not all day")
    description: Optional[str] = Field(
        None, description="Optional description of the holiday"
    )

    @model_validator(mode="after")
    def validate_time_constraint(self):
        """Validate that if not all_day, both start_time and end_time are provided"""
        if not self.all_day:
            if self.start_time is None or self.end_time is None:
                raise ValueError(
                    "start_time and end_time are required when all_day is False"
                )
            if self.start_time >= self.end_time:
                raise ValueError("start_time must be before end_time")
        return self


class HolidayCreateRequest(HolidayBase):
    """Schema for creating a new holiday"""

    class Config:
        json_schema_extra = {
            "example": {
                "holiday_type": "PREDEFINED",
                "date": "2024-12-25",
                "all_day": True,
                "start_time": None,
                "end_time": None,
                "description": "Christmas Day",
            }
        }


class HolidayUpdateRequest(BaseModel):
    """Schema for updating an existing holiday"""

    holiday_type: Optional[Literal["PREDEFINED", "PERSONAL"]] = Field(
        None,
        description="Type of holiday: PREDEFINED for company-wide holidays, PERSONAL for individual holidays",
    )
    date: Optional[date] = Field(None, description="Holiday date")
    all_day: Optional[bool] = Field(None, description="Whether the holiday is all day")
    start_time: Optional[time] = Field(None, description="Start time if not all day")
    end_time: Optional[time] = Field(None, description="End time if not all day")
    description: Optional[str] = Field(
        None, description="Optional description of the holiday"
    )
    is_active: Optional[bool] = Field(None, description="Whether the holiday is active")

    @model_validator(mode="after")
    def validate_time_constraint(self):
        """Validate that if all_day is False, both start_time and end_time are provided"""
        if self.all_day is not None and not self.all_day:
            if self.start_time is None or self.end_time is None:
                raise ValueError(
                    "start_time and end_time are required when all_day is False"
                )
            if self.start_time >= self.end_time:
                raise ValueError("start_time must be before end_time")
        return self

    class Config:
        json_schema_extra = {
            "example": {
                "holiday_type": "PERSONAL",
                "date": "2024-07-04",
                "all_day": False,
                "start_time": "09:00:00",
                "end_time": "17:00:00",
                "description": "Independence Day - Half day",
                "is_active": True,
            }
        }


class HolidayResponse(HolidayBase):
    """Schema for holiday response"""

    id: UUID = Field(..., description="Holiday unique identifier")
    is_active: bool = Field(..., description="Whether the holiday is active")
    is_deleted: bool = Field(..., description="Whether the holiday is soft deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "holiday_type": "PREDEFINED",
                "date": "2024-12-25",
                "all_day": True,
                "start_time": None,
                "end_time": None,
                "description": "Christmas Day",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "deleted_at": None,
            }
        }


class HolidayListResponse(BaseModel):
    """Schema for holiday list response with pagination"""

    items: list[HolidayResponse] = Field(..., description="List of holidays")
    total_count: int = Field(..., description="Total number of holidays")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "holiday_type": "PREDEFINED",
                        "date": "2024-12-25",
                        "all_day": True,
                        "start_time": None,
                        "end_time": None,
                        "description": "Christmas Day",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z",
                        "deleted_at": None,
                    }
                ],
                "total_count": 1,
            }
        }
